import { useTranslation } from "react-i18next";

function AboutUs() {
    const { t } = useTranslation();

    return (
        <div className="min-h-screen  text-white">
            {/* Hero Section */}
            <div className="relative">
                {/* Gradient Background Section */}
                <div className="pt-32 border-2 pb-32 text-center bg-gradient-to-br from-purple-600 via-purple-500 to-indigo-500">
                    <h1 className="text-4xl md:text-6xl font-bold mb-6">
                        {t('about.title') || 'О нас'}
                    </h1>
                    <p className="text-lg md:text-xl text-white/90 max-w-3xl mx-auto leading-relaxed pb-32">
                        {/* Translate it */}
                        Современная платформа для аренды жилья с системой умных замков —
                        безопасно, удобно и доступно для всех.
                    </p>
                </div>
                {/* Video Section - shifted upward so background appears behind half */}
                <div className="relative top-5 -mt-40 mx-auto w-1/2 z-10">
                    <video
                        src="/videos/ToGolock.mp4"
                        autoPlay
                        muted
                        loop
                        playsInline
                        className="rounded-md w-full h-auto"
                    />
                </div>
            </div>

            <div className="container mx-auto px-4 py-16">
                {/* Main content sections */}
                <div className="max-w-4xl mx-auto space-y-16">
                    {/* Who are we section */}
                    <div>
                        <div className="bg-white rounded-2xl p-8 md:p-12">
                            <h2 className="text-3xl font-bold text-gray-800 mb-6">Кто мы?</h2>
                            <div className="flex flex-col md:flex-row items-center gap-8">
                                <div className="w-full">
                                    <img src="/3dkey.png" className="w-full h-auto" alt="" />
                                </div>
                                <div className="text-gray-700 text-lg leading-relaxed">
                                    <p dangerouslySetInnerHTML={{ __html: t('about.description.p1') || 'ToGoLock — это современное решение для краткосрочной аренды жилья с интегрированной системой умных замков.' }} />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* What's our specialty section */}
                    <div className="relative">
                        <div className="bg-white rounded-2xl p-8 md:p-12">
                            <h2 className="text-3xl font-bold text-gray-800 mb-6">В чем наша особенность?</h2>
                            <div className="flex flex-col md:flex-row items-center gap-8">
                                <div className="w-11/12">
                                    <img src="/3dlock.png" className="w-full h-auto" alt="" />
                                </div>
                                <div className="text-gray-700 text-lg leading-relaxed">
                                    <p dangerouslySetInnerHTML={{ __html: t('about.description.p2') || 'Наша система умных замков позволяет арендаторам получать доступ к жилью без физических ключей, обеспечивая максимальную безопасность и удобство.' }} />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Who is TOGOLOCK created for section */}
                    <div className="relative">
                        <div className="bg-white rounded-2xl p-8 md:p-12">
                            <div className="flex flex-col md:flex-row items-center gap-8">
                                <div className="md:w-1/3 flex justify-center">
                                    <div className="w-40 h-40 bg-gradient-to-br from-green-400 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg">
                                        <svg className="w-20 h-20 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                        </svg>
                                    </div>
                                </div>
                                <div className="md:w-2/3">
                                    <h2 className="text-3xl font-bold text-gray-800 mb-6">Для кого создан TOGOLOCK?</h2>
                                    <div className="text-gray-700 text-lg leading-relaxed">
                                        <p>{t('about.description.p3') || 'Для владельцев недвижимости, желающих сдавать жилье без лишних хлопот, и для путешественников, ценящих комфорт и безопасность.'}</p>
                                        <ul className="mt-4 space-y-2">
                                            <li className="flex items-center">
                                                <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                                                Для владельцев квартир и домов
                                            </li>
                                            <li className="flex items-center">
                                                <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                                                Для бизнес-путешественников
                                            </li>
                                            <li className="flex items-center">
                                                <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                                                Для туристов и отдыхающих
                                            </li>
                                            <li className="flex items-center">
                                                <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                                                Для людей, ищущих временное жилье
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Advantages section */}
                <div className="mt-24 max-w-6xl mx-auto">
                    <h2 className="text-3xl md:text-4xl font-bold text-center mb-16">
                        {t('about.advantagesTitle') || 'Наши преимущества'}
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {[1, 2, 3, 4].map(num => (
                            <div key={num} className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300">
                                <div className="flex items-center mb-4">
                                    <span className="text-2xl font-bold text-white/60 mr-4">0{num}</span>
                                    <div className="h-px bg-white/30 flex-1"></div>
                                </div>
                                <h3 className="font-bold text-2xl mb-4 text-white">
                                    {t(`about.advantages.0${num}.title`)}
                                </h3>
                                <p className="text-white/80 text-lg leading-relaxed">
                                    {t(`about.advantages.0${num}.text`)}
                                </p>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Footer section with app download */}
                <div className="mt-24 text-center">
                    <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-white/20">
                        <h3 className="text-2xl md:text-3xl font-bold mb-6">Скачайте наше приложение</h3>
                        <p className="text-white/80 mb-8 text-lg">
                            Управляйте бронированиями и замками прямо с вашего телефона
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <a href="#" className="inline-flex items-center bg-black text-white px-6 py-3 rounded-xl hover:bg-gray-800 transition-colors">
                                <img src="/appStore.svg" alt="App Store" className="w-6 h-6 mr-2" />
                                App Store
                            </a>
                            <a href="#" className="inline-flex items-center bg-black text-white px-6 py-3 rounded-xl hover:bg-gray-800 transition-colors">
                                <img src="/googlePlay.svg" alt="Google Play" className="w-6 h-6 mr-2" />
                                Google Play
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default AboutUs;
